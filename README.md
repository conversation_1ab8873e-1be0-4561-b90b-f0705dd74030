# BigCommerce Custom App - Native PHP

A minimal BigCommerce custom app built with **native PHP only** - no external dependencies required! This follows the official BigCommerce documentation guidelines for minimal setup.

## Overview

This app demonstrates the core functionality required for a BigCommerce custom app using only native PHP:

- OAuth 2.0 authentication flow
- Webhook handling
- BigCommerce API integration
- File-based session storage (no database required)

## Core Files

Following BigCommerce's recommended minimal setup, this app consists of **3 core files**:

1. **`index.php`** - Main application with all routes and logic (native PHP)
2. **`composer.json`** - Minimal composer file (no dependencies)
3. **`.env`** - Configuration file (copy from `.env.example`)

## Key Features

✅ **Zero Dependencies** - Uses only native PHP functions
✅ **No Database Required** - File-based storage system
✅ **No Redis/External Services** - Everything runs with just PHP
✅ **Easy Deployment** - Works on any PHP hosting environment
✅ **Complete OAuth Flow** - Full BigCommerce authentication
✅ **API Integration** - Native cURL-based BigCommerce API client
✅ **Webhook Support** - Handle BigCommerce events

## Prerequisites

- PHP 7.4 or higher (PHP 8.1+ recommended)
- Web server (Apache/Nginx) or local development server
- cURL extension enabled (usually enabled by default)
- BigCommerce store (sandbox recommended for development)
- BigCommerce Developer Portal account

## Installation

1. **Clone or download this repository**

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and fill in your BigCommerce app credentials:
   - `BC_CLIENT_ID` - Your app's Client ID from Developer Portal
   - `BC_CLIENT_SECRET` - Your app's Client Secret from Developer Portal
   - `BC_CALLBACK_URL` - Your app's callback URL (e.g., `https://yourdomain.com/auth/callback`)

3. **Set proper permissions for storage directory**
   ```bash
   chmod 755 storage/
   ```
   (The storage directory will be created automatically when the app runs)

4. **Start the web server**

   For development, you can use PHP's built-in server:
   ```bash
   php -S localhost:8000
   ```

   Or configure your web server to serve the app directory.

## BigCommerce App Registration

1. Go to [BigCommerce Developer Portal](https://devtools.bigcommerce.com/)
2. Click "Create an app"
3. Fill in the app details
4. In the Technical section, set:
   - **Auth Callback URL**: `https://yourdomain.com/auth/callback`
   - **Load Callback URL**: `https://yourdomain.com/load`
   - **Uninstall Callback URL**: `https://yourdomain.com/uninstall`
   - **Remove User Callback URL**: `https://yourdomain.com/remove-user` (for multi-user apps)
5. Set OAuth scopes as needed (e.g., Products - Read Only)
6. Save and get your Client ID and Client Secret

## App Routes

- `GET /` - Home page with basic app information
- `GET /auth/callback` - OAuth callback for app installation
- `GET /load` - App loading endpoint (displays in BigCommerce control panel)
- `GET /uninstall` - App uninstall callback
- `GET /remove-user` - Remove user callback (multi-user apps)
- `POST /webhooks` - Webhook endpoint for BigCommerce events

## Features

- **OAuth 2.0 Flow**: Complete implementation of BigCommerce's OAuth flow using native PHP
- **API Integration**: Custom BigCommerce API client using cURL (no external libraries)
- **Webhook Support**: Ready to handle BigCommerce webhook events with signature verification
- **File-based Storage**: Simple file-based session storage (no database required)
- **Security**: Includes signature verification for webhooks and signed requests
- **Error Handling**: Proper error handling and logging
- **Easy Deployment**: Works on any standard PHP hosting environment

## Development

For local development with ngrok (recommended):

1. Install ngrok: `npm install -g ngrok` or download from [ngrok.com](https://ngrok.com)
2. Start your local server: `php -S localhost:8000`
3. In another terminal: `ngrok http 8000`
4. Use the ngrok URL in your BigCommerce app configuration

## Architecture

This app uses a simple, native PHP architecture:

- **SimpleStorage Class**: File-based key-value storage system
- **SimpleHttpClient Class**: cURL-based HTTP client for API calls
- **SimpleBigCommerceAPI Class**: Native BigCommerce API client
- **Route Handlers**: Individual functions for each endpoint
- **Helper Functions**: Utility functions for common operations

## Security Considerations

- Always verify signed requests and webhook signatures
- Use HTTPS in production
- Keep your client secret secure
- Implement proper error handling
- File permissions are set securely for storage directory
- All user input is properly sanitized

## Deployment

This app can be deployed to any PHP hosting environment:

1. Upload all files to your web server
2. Ensure PHP 7.4+ is available
3. Set proper file permissions for the storage directory
4. Configure your `.env` file with production values
5. Update your BigCommerce app URLs in the Developer Portal

## Next Steps

This minimal app provides the foundation for building more complex BigCommerce integrations. You can extend it by:

- Adding a proper database for persistent storage (MySQL, PostgreSQL, etc.)
- Implementing more sophisticated UI with templates
- Adding more webhook event handlers
- Integrating with external services
- Adding user management features
- Implementing caching mechanisms
- Adding logging to files or external services

## Support

For BigCommerce API documentation and support:
- [BigCommerce Developer Portal](https://developer.bigcommerce.com/)
- [BigCommerce API Documentation](https://developer.bigcommerce.com/api-docs/)
- [BigCommerce Developer Community](https://developer.bigcommerce.com/community)
