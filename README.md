# BigCommerce Custom App - PHP

A minimal BigCommerce custom app built with PHP following the official BigCommerce documentation guidelines.

## Overview

This app demonstrates the core functionality required for a BigCommerce custom app:

- OAuth 2.0 authentication flow
- Webhook handling
- BigCommerce API integration
- Secure session management

## Core Files

Following BigCommerce's recommended minimal setup, this app consists of 3 core files:

1. **`index.php`** - Main application with all routes and logic
2. **`composer.json`** - PHP dependencies management
3. **`.env`** - Configuration file (copy from `.env.example`)

## Prerequisites

- PHP 8.1 or higher
- Composer
- Redis server (for session storage)
- Web server (Apache/Nginx) or local development server
- BigCommerce store (sandbox recommended for development)
- BigCommerce Developer Portal account

## Installation

1. **Clone or download this repository**

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and fill in your BigCommerce app credentials:
   - `BC_CLIENT_ID` - Your app's Client ID from Developer Portal
   - `BC_CLIENT_SECRET` - Your app's Client Secret from Developer Portal
   - `BC_CALLBACK_URL` - Your app's callback URL (e.g., `https://yourdomain.com/auth/callback`)

4. **Start Redis server** (if not already running)
   ```bash
   redis-server
   ```

5. **Start the web server**
   
   For development, you can use PHP's built-in server:
   ```bash
   php -S localhost:8000
   ```
   
   Or configure your web server to serve the app directory.

## BigCommerce App Registration

1. Go to [BigCommerce Developer Portal](https://devtools.bigcommerce.com/)
2. Click "Create an app"
3. Fill in the app details
4. In the Technical section, set:
   - **Auth Callback URL**: `https://yourdomain.com/auth/callback`
   - **Load Callback URL**: `https://yourdomain.com/load`
   - **Uninstall Callback URL**: `https://yourdomain.com/uninstall`
   - **Remove User Callback URL**: `https://yourdomain.com/remove-user` (for multi-user apps)
5. Set OAuth scopes as needed (e.g., Products - Read Only)
6. Save and get your Client ID and Client Secret

## App Routes

- `GET /` - Home page with basic app information
- `GET /auth/callback` - OAuth callback for app installation
- `GET /load` - App loading endpoint (displays in BigCommerce control panel)
- `GET /uninstall` - App uninstall callback
- `GET /remove-user` - Remove user callback (multi-user apps)
- `POST /webhooks` - Webhook endpoint for BigCommerce events

## Features

- **OAuth 2.0 Flow**: Complete implementation of BigCommerce's OAuth flow
- **API Integration**: Demonstrates how to use BigCommerce API to fetch store and product data
- **Webhook Support**: Ready to handle BigCommerce webhook events
- **Session Management**: Uses Redis for secure session storage
- **Security**: Includes signature verification for webhooks and signed requests
- **Error Handling**: Proper error handling and logging

## Development

For local development with ngrok (recommended):

1. Install ngrok: `npm install -g ngrok`
2. Start your local server: `php -S localhost:8000`
3. In another terminal: `ngrok http 8000`
4. Use the ngrok URL in your BigCommerce app configuration

## Security Considerations

- Always verify signed requests and webhook signatures
- Use HTTPS in production
- Keep your client secret secure
- Implement proper error handling
- Use secure session storage (Redis recommended)

## Next Steps

This minimal app provides the foundation for building more complex BigCommerce integrations. You can extend it by:

- Adding a proper database for persistent storage
- Implementing more sophisticated UI
- Adding more webhook event handlers
- Integrating with external services
- Adding user management features

## Support

For BigCommerce API documentation and support:
- [BigCommerce Developer Portal](https://developer.bigcommerce.com/)
- [BigCommerce API Documentation](https://developer.bigcommerce.com/api-docs/)
- [BigCommerce Developer Community](https://developer.bigcommerce.com/community)
