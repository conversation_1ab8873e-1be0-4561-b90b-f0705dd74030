# BigCommerce App Configuration
BC_CLIENT_ID=your_client_id_here
BC_CLIENT_SECRET=your_client_secret_here
BC_REDIRECT_URI=https://yourdomain.com/auth/callback
BC_WEBHOOK_SECRET=your_webhook_secret_here

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=bigcommerce_app
DB_USERNAME=root
DB_PASSWORD=

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=https://yourdomain.com
APP_SECRET_KEY=your_secret_key_here

# Session Configuration
SESSION_LIFETIME=7200
SESSION_SECURE=false
SESSION_HTTP_ONLY=true

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# API Configuration
API_RATE_LIMIT=40
API_TIMEOUT=30

# Security Configuration
CSRF_TOKEN_LIFETIME=3600
WEBHOOK_VERIFY_SSL=true
