<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\App;
use App\Services\Router;
use App\Services\Logger;
use App\Exceptions\AppException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

try {
    // Initialize configuration
    $config = App::getInstance();
    
    // Initialize logger
    $logger = Logger::getInstance();
    
    // Create request object
    $request = Request::createFromGlobals();
    
    // Initialize router
    $router = new Router();
    
    // Define routes
    $router->get('/', 'HomeController@index');
    $router->get('/install', 'AuthController@install');
    $router->get('/auth/callback', 'AuthController@callback');
    $router->get('/dashboard', 'DashboardController@index');
    $router->post('/webhooks', 'WebhookController@handle');
    $router->get('/uninstall', 'AuthController@uninstall');
    
    // Handle the request
    $response = $router->handle($request);
    
    // Send response
    $response->send();
    
} catch (AppException $e) {
    $logger = Logger::getInstance();
    $logger->error('Application error: ' . $e->getMessage(), [
        'exception' => $e,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'method' => $_SERVER['REQUEST_METHOD'] ?? '',
    ]);
    
    $response = new Response(
        $config->isDebug() ? $e->getMessage() : 'An error occurred',
        $e->getCode() ?: 500,
        ['Content-Type' => 'text/html']
    );
    $response->send();
    
} catch (Throwable $e) {
    $logger = Logger::getInstance();
    $logger->critical('Critical error: ' . $e->getMessage(), [
        'exception' => $e,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'method' => $_SERVER['REQUEST_METHOD'] ?? '',
    ]);
    
    $response = new Response(
        $config->isDebug() ? $e->getMessage() : 'A critical error occurred',
        500,
        ['Content-Type' => 'text/html']
    );
    $response->send();
}
