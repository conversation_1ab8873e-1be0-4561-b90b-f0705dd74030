<?php

/**
 * Simple test script to verify the BigCommerce app works
 */

echo "BigCommerce App Test\n";
echo "===================\n\n";

// Test 1: Check PHP version
echo "1. PHP Version: " . PHP_VERSION;
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    echo " ✓ OK\n";
} else {
    echo " ✗ FAIL (requires PHP 7.4+)\n";
}

// Test 2: Check cURL extension
echo "2. cURL Extension: ";
if (extension_loaded('curl')) {
    echo "✓ Available\n";
} else {
    echo "✗ Missing (required for API calls)\n";
}

// Test 3: Check file permissions
echo "3. Storage Directory: ";
$storageDir = __DIR__ . '/storage';
if (!is_dir($storageDir)) {
    mkdir($storageDir, 0755, true);
}
if (is_writable($storageDir)) {
    echo "✓ Writable\n";
} else {
    echo "✗ Not writable (check permissions)\n";
}

// Test 4: Check .env file
echo "4. Environment File: ";
if (file_exists(__DIR__ . '/.env')) {
    echo "✓ Found\n";
} else {
    echo "⚠ Missing (copy .env.example to .env)\n";
}

// Test 5: Test storage functionality
echo "5. Storage Test: ";
try {
    require_once __DIR__ . '/index.php';
    // This will include our SimpleStorage class
    
    // Create a test instance (but don't run the main app)
    $testStorage = new SimpleStorage('test_storage');
    $testStorage->set('test_key', 'test_value');
    $result = $testStorage->get('test_key');
    
    if ($result === 'test_value') {
        echo "✓ Working\n";
        // Clean up
        $testStorage->delete('test_key');
        rmdir(__DIR__ . '/test_storage');
    } else {
        echo "✗ Failed\n";
    }
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
echo "\nNext steps:\n";
echo "1. Copy .env.example to .env\n";
echo "2. Fill in your BigCommerce app credentials in .env\n";
echo "3. Start the server: php -S localhost:8000\n";
echo "4. Use ngrok for public access: ngrok http 8000\n";
echo "5. Register your app in BigCommerce Developer Portal\n";
