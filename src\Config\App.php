<?php

namespace App\Config;

use Dotenv\Dotenv;

class App
{
    private static ?App $instance = null;
    private array $config = [];

    private function __construct()
    {
        $this->loadEnvironment();
        $this->loadConfiguration();
    }

    public static function getInstance(): App
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvironment(): void
    {
        $dotenv = Dotenv::createImmutable(dirname(__DIR__, 2));
        $dotenv->safeLoad();
    }

    private function loadConfiguration(): void
    {
        $this->config = [
            'app' => [
                'env' => $_ENV['APP_ENV'] ?? 'production',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'url' => $_ENV['APP_URL'] ?? 'https://localhost',
                'secret_key' => $_ENV['APP_SECRET_KEY'] ?? '',
            ],
            'bigcommerce' => [
                'client_id' => $_ENV['BC_CLIENT_ID'] ?? '',
                'client_secret' => $_ENV['BC_CLIENT_SECRET'] ?? '',
                'redirect_uri' => $_ENV['BC_REDIRECT_URI'] ?? '',
                'webhook_secret' => $_ENV['BC_WEBHOOK_SECRET'] ?? '',
                'api_url' => 'https://api.bigcommerce.com',
                'auth_url' => 'https://login.bigcommerce.com/oauth2',
            ],
            'database' => [
                'host' => $_ENV['DB_HOST'] ?? 'localhost',
                'port' => $_ENV['DB_PORT'] ?? '3306',
                'database' => $_ENV['DB_DATABASE'] ?? '',
                'username' => $_ENV['DB_USERNAME'] ?? '',
                'password' => $_ENV['DB_PASSWORD'] ?? '',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
            ],
            'session' => [
                'lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 7200),
                'secure' => filter_var($_ENV['SESSION_SECURE'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'http_only' => filter_var($_ENV['SESSION_HTTP_ONLY'] ?? true, FILTER_VALIDATE_BOOLEAN),
            ],
            'logging' => [
                'level' => $_ENV['LOG_LEVEL'] ?? 'info',
                'file' => $_ENV['LOG_FILE'] ?? 'logs/app.log',
            ],
            'api' => [
                'rate_limit' => (int)($_ENV['API_RATE_LIMIT'] ?? 40),
                'timeout' => (int)($_ENV['API_TIMEOUT'] ?? 30),
            ],
            'security' => [
                'csrf_token_lifetime' => (int)($_ENV['CSRF_TOKEN_LIFETIME'] ?? 3600),
                'webhook_verify_ssl' => filter_var($_ENV['WEBHOOK_VERIFY_SSL'] ?? true, FILTER_VALIDATE_BOOLEAN),
            ],
        ];
    }

    public function get(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    public function all(): array
    {
        return $this->config;
    }

    public function isDebug(): bool
    {
        return $this->get('app.debug', false);
    }

    public function getEnvironment(): string
    {
        return $this->get('app.env', 'production');
    }
}
