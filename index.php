<?php

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;
use Silex\Application;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use GuzzleHttp\Client;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Bigcommerce\Api\Client as Bigcommerce;
use Predis\Client as RedisClient;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

// Create Silex application
$app = new Application();
$app['debug'] = $_ENV['APP_DEBUG'] ?? false;

// Initialize Redis client
$redis = new RedisClient([
    'scheme' => 'tcp',
    'host'   => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
    'port'   => $_ENV['REDIS_PORT'] ?? 6379,
    'password' => $_ENV['REDIS_PASSWORD'] ?? null,
]);

/**
 * Home route - Basic app information
 */
$app->get('/', function () {
    return new Response('
        <h1>BigCommerce Custom App</h1>
        <p>This is a custom BigCommerce application.</p>
        <p>To install this app, register it in the BigCommerce Developer Portal and install it from your store\'s control panel.</p>
    ', 200, ['Content-Type' => 'text/html']);
});

/**
 * OAuth callback - Handle the authorization code exchange
 */
$app->get('/auth/callback', function (Request $request) use ($app, $redis) {
    $code = $request->get('code');
    $scope = $request->get('scope');
    $context = $request->get('context');
    
    if (!$code || !$context) {
        return new Response('Missing required parameters', 400);
    }
    
    // Exchange authorization code for access token
    $payload = [
        'client_id' => getClientId(),
        'client_secret' => getClientSecret(),
        'redirect_uri' => getCallbackUrl(),
        'grant_type' => 'authorization_code',
        'code' => $code,
        'scope' => $scope,
        'context' => $context,
    ];
    
    try {
        $client = new Client();
        $response = $client->post(getAuthService() . '/oauth2/token', [
            'form_params' => $payload,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
            ]
        ]);
        
        if ($response->getStatusCode() === 200) {
            $data = json_decode($response->getBody(), true);
            
            // Extract store hash from context
            list($contextType, $storeHash) = explode('/', $data['context'], 2);
            
            // Store auth data in Redis
            $authKey = "stores/{$storeHash}/auth";
            $userKey = getUserKey($storeHash, $data['user']['email']);
            
            $redis->set($authKey, json_encode($data));
            $redis->set($userKey, json_encode($data['user']));
            
            return new Response('
                <h1>Authorization Successful!</h1>
                <p>Your app has been successfully installed.</p>
                <p>Store Hash: ' . htmlspecialchars($storeHash) . '</p>
                <p>User: ' . htmlspecialchars($data['user']['email']) . '</p>
                <p>You can now close this window and return to your BigCommerce control panel.</p>
            ', 200, ['Content-Type' => 'text/html']);
        }
    } catch (Exception $e) {
        error_log('OAuth callback error: ' . $e->getMessage());
        return new Response('Authorization failed: ' . $e->getMessage(), 500);
    }
    
    return new Response('Authorization failed', 500);
});

/**
 * Load callback - Called when the app is loaded in the BigCommerce control panel
 */
$app->get('/load', function (Request $request) use ($app, $redis) {
    $signedPayload = $request->get('signed_payload');
    
    if (!$signedPayload) {
        return new Response('Missing signed_payload', 400);
    }
    
    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return new Response('Invalid signed_payload', 401);
    }
    
    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];
    
    // Get or create user data
    $userKey = getUserKey($storeHash, $userEmail);
    $user = json_decode($redis->get($userKey), true);
    
    if (!$user) {
        $user = $data['user'];
        $redis->set($userKey, json_encode($user));
    }
    
    // Configure BigCommerce API
    configureBCApi($storeHash, $redis);
    
    // Get store information
    try {
        $store = Bigcommerce::getStore();
        $products = Bigcommerce::getProducts(['limit' => 5]);
        
        $html = '
        <h1>Welcome to Your BigCommerce App</h1>
        <h2>Store Information</h2>
        <p><strong>Store Name:</strong> ' . htmlspecialchars($store->name) . '</p>
        <p><strong>Store URL:</strong> ' . htmlspecialchars($store->domain) . '</p>
        <p><strong>User:</strong> ' . htmlspecialchars($user['email']) . '</p>
        
        <h2>Recent Products</h2>
        <ul>';
        
        foreach ($products as $product) {
            $html .= '<li>' . htmlspecialchars($product->name) . ' - $' . number_format($product->price, 2) . '</li>';
        }
        
        $html .= '</ul>
        <p><em>This is a basic demonstration of your BigCommerce app integration.</em></p>';
        
        return new Response($html, 200, ['Content-Type' => 'text/html']);
        
    } catch (Exception $e) {
        error_log('Load callback error: ' . $e->getMessage());
        return new Response('Error loading app: ' . $e->getMessage(), 500);
    }
});

/**
 * Uninstall callback - Called when the app is uninstalled
 */
$app->get('/uninstall', function (Request $request) use ($app, $redis) {
    $signedPayload = $request->get('signed_payload');
    
    if (!$signedPayload) {
        return new Response('Missing signed_payload', 400);
    }
    
    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return new Response('Invalid signed_payload', 401);
    }
    
    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];
    
    // Clean up stored data
    $authKey = "stores/{$storeHash}/auth";
    $userKey = getUserKey($storeHash, $userEmail);
    
    $redis->del($authKey);
    $redis->del($userKey);
    
    return new Response('App uninstalled successfully', 200);
});

/**
 * Remove user callback - Called when a user is removed (multi-user apps)
 */
$app->get('/remove-user', function (Request $request) use ($app, $redis) {
    $signedPayload = $request->get('signed_payload');
    
    if (!$signedPayload) {
        return new Response('Missing signed_payload', 400);
    }
    
    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return new Response('Invalid signed_payload', 401);
    }
    
    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];
    
    // Remove user data
    $userKey = getUserKey($storeHash, $userEmail);
    $redis->del($userKey);
    
    return new Response('User removed successfully', 200);
});

/**
 * Webhook endpoint - Handle BigCommerce webhooks
 */
$app->post('/webhooks', function (Request $request) use ($app) {
    // Verify webhook signature
    $payload = $request->getContent();
    $signature = $request->headers->get('X-BC-Webhook-Signature');
    
    if (!verifyWebhookSignature($payload, $signature)) {
        return new Response('Invalid webhook signature', 401);
    }
    
    $data = json_decode($payload, true);
    
    // Log webhook for debugging
    error_log('Webhook received: ' . $payload);
    
    // Handle different webhook events
    switch ($data['scope'] ?? '') {
        case 'store/order/created':
            // Handle new order
            break;
        case 'store/product/updated':
            // Handle product update
            break;
        default:
            // Handle other events
            break;
    }
    
    return new Response('Webhook processed', 200);
});

// Helper functions
function getClientId() {
    return $_ENV['BC_CLIENT_ID'] ?? '';
}

function getClientSecret() {
    return $_ENV['BC_CLIENT_SECRET'] ?? '';
}

function getCallbackUrl() {
    return $_ENV['BC_CALLBACK_URL'] ?? '';
}

function getAuthService() {
    return $_ENV['BC_AUTH_SERVICE'] ?? 'https://login.bigcommerce.com';
}

function getUserKey($storeHash, $email) {
    return "app:users:{$storeHash}:{$email}";
}

function verifySignedRequest($signedRequest) {
    if (!$signedRequest) {
        return null;
    }
    
    $parts = explode('.', $signedRequest, 2);
    if (count($parts) !== 2) {
        return null;
    }
    
    list($encodedData, $encodedSignature) = $parts;
    
    $signature = base64_decode($encodedSignature);
    $jsonStr = base64_decode($encodedData);
    $data = json_decode($jsonStr, true);
    
    $expectedSignature = hash_hmac('sha256', $jsonStr, getClientSecret(), false);
    
    if (!hash_equals($expectedSignature, $signature)) {
        error_log('Invalid signed request signature');
        return null;
    }
    
    return $data;
}

function verifyWebhookSignature($payload, $signature) {
    $expectedSignature = hash_hmac('sha256', $payload, getClientSecret(), false);
    return hash_equals($expectedSignature, $signature);
}

function configureBCApi($storeHash, $redis) {
    $authData = json_decode($redis->get("stores/{$storeHash}/auth"), true);
    
    if (!$authData) {
        throw new Exception('No auth data found for store');
    }
    
    Bigcommerce::configure([
        'client_id' => getClientId(),
        'auth_token' => $authData['access_token'],
        'store_hash' => $storeHash
    ]);
}

// Run the application
$app->run();
