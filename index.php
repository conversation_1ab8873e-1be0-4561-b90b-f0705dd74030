<?php

/**
 * BigCommerce Custom App - Native PHP Implementation
 * No external dependencies required
 */

// Load environment variables from .env file
function loadEnv() {
    if (file_exists(__DIR__ . '/.env')) {
        $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue; // Skip comments
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Simple session storage using files (alternative to Redis)
class SimpleStorage {
    private $storageDir;

    public function __construct($dir = 'storage') {
        $this->storageDir = __DIR__ . '/' . $dir;
        if (!is_dir($this->storageDir)) {
            mkdir($this->storageDir, 0755, true);
        }
    }

    public function set($key, $value) {
        $filename = $this->storageDir . '/' . md5($key) . '.json';
        file_put_contents($filename, json_encode([
            'key' => $key,
            'value' => $value,
            'timestamp' => time()
        ]));
    }

    public function get($key) {
        $filename = $this->storageDir . '/' . md5($key) . '.json';
        if (!file_exists($filename)) {
            return null;
        }
        $data = json_decode(file_get_contents($filename), true);
        return $data['value'] ?? null;
    }

    public function delete($key) {
        $filename = $this->storageDir . '/' . md5($key) . '.json';
        if (file_exists($filename)) {
            unlink($filename);
        }
    }
}

// Load environment variables
loadEnv();

// Initialize storage
$storage = new SimpleStorage();

// Simple HTTP client using cURL
class SimpleHttpClient {
    public function post($url, $data, $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ['body' => $response, 'status' => $httpCode];
    }

    public function get($url, $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ['body' => $response, 'status' => $httpCode];
    }
}

// Simple BigCommerce API client
class SimpleBigCommerceAPI {
    private $storeHash;
    private $accessToken;
    private $clientId;
    private $httpClient;

    public function __construct($storeHash, $accessToken, $clientId) {
        $this->storeHash = $storeHash;
        $this->accessToken = $accessToken;
        $this->clientId = $clientId;
        $this->httpClient = new SimpleHttpClient();
    }

    public function getStore() {
        $url = "https://api.bigcommerce.com/stores/{$this->storeHash}/v2/store";
        $headers = [
            "X-Auth-Token: {$this->accessToken}",
            "X-Auth-Client: {$this->clientId}",
            "Accept: application/json"
        ];

        $response = $this->httpClient->get($url, $headers);
        if ($response['status'] === 200) {
            return json_decode($response['body'], true);
        }
        return null;
    }

    public function getProducts($limit = 10) {
        $url = "https://api.bigcommerce.com/stores/{$this->storeHash}/v3/catalog/products?limit={$limit}";
        $headers = [
            "X-Auth-Token: {$this->accessToken}",
            "X-Auth-Client: {$this->clientId}",
            "Accept: application/json"
        ];

        $response = $this->httpClient->get($url, $headers);
        if ($response['status'] === 200) {
            $data = json_decode($response['body'], true);
            return $data['data'] ?? [];
        }
        return [];
    }
}

// Simple router
function handleRequest() {
    $method = $_SERVER['REQUEST_METHOD'];
    $fullPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

    // Remove the base path if it exists
    $basePath = '/php/custom_bigcommerce_app';
    if (strpos($fullPath, $basePath) === 0) {
        $path = substr($fullPath, strlen($basePath));
    } else {
        $path = $fullPath;
    }

    // Ensure path starts with /
    if (empty($path) || $path === '') {
        $path = '/';
    }

    switch ($path) {
        case '/':
        case '/index.php':
            return handleHome();
        case '/auth/callback':
            return handleAuthCallback();
        case '/load':
            return handleLoad();
        case '/uninstall':
            return handleUninstall();
        case '/remove-user':
            return handleRemoveUser();
        case '/webhooks':
            if ($method === 'POST') {
                return handleWebhook();
            }
            break;
    }

    return ['status' => 404, 'body' => 'Not Found - Path: ' . htmlspecialchars($path)];
}

// Route handlers
function handleHome() {
    return [
        'status' => 200,
        'headers' => ['Content-Type' => 'text/html'],
        'body' => '
            <h1>BigCommerce Custom App</h1>
            <p>This is a custom BigCommerce application built with native PHP.</p>
            <p>To install this app, register it in the BigCommerce Developer Portal and install it from your store\'s control panel.</p>
        '
    ];
}

function handleAuthCallback() {
    global $storage;

    $code = $_GET['code'] ?? '';
    $scope = $_GET['scope'] ?? '';
    $context = $_GET['context'] ?? '';

    if (!$code || !$context) {
        return ['status' => 400, 'body' => 'Missing required parameters'];
    }

    // Exchange authorization code for access token
    $payload = [
        'client_id' => getClientId(),
        'client_secret' => getClientSecret(),
        'redirect_uri' => getCallbackUrl(),
        'grant_type' => 'authorization_code',
        'code' => $code,
        'scope' => $scope,
        'context' => $context,
    ];

    $httpClient = new SimpleHttpClient();
    $response = $httpClient->post(getAuthService() . '/oauth2/token', $payload, [
        'Accept: application/json',
        'Content-Type: application/x-www-form-urlencoded'
    ]);

    if ($response['status'] === 200) {
        $data = json_decode($response['body'], true);

        // Extract store hash from context
        list($contextType, $storeHash) = explode('/', $data['context'], 2);

        // Store auth data
        $authKey = "stores/{$storeHash}/auth";
        $userKey = getUserKey($storeHash, $data['user']['email']);

        $storage->set($authKey, $data);
        $storage->set($userKey, $data['user']);

        return [
            'status' => 200,
            'headers' => ['Content-Type' => 'text/html'],
            'body' => '
                <h1>Authorization Successful!</h1>
                <p>Your app has been successfully installed.</p>
                <p>Store Hash: ' . htmlspecialchars($storeHash) . '</p>
                <p>User: ' . htmlspecialchars($data['user']['email']) . '</p>
                <p>You can now close this window and return to your BigCommerce control panel.</p>
            '
        ];
    }

    return ['status' => 500, 'body' => 'Authorization failed'];
}

function handleLoad() {
    global $storage;

    $signedPayload = $_GET['signed_payload'] ?? '';

    if (!$signedPayload) {
        return ['status' => 400, 'body' => 'Missing signed_payload'];
    }

    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return ['status' => 401, 'body' => 'Invalid signed_payload'];
    }

    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];

    // Get or create user data
    $userKey = getUserKey($storeHash, $userEmail);
    $user = $storage->get($userKey);

    if (!$user) {
        $user = $data['user'];
        $storage->set($userKey, $user);
    }

    // Get auth data and create API client
    $authKey = "stores/{$storeHash}/auth";
    $authData = $storage->get($authKey);

    if (!$authData) {
        return ['status' => 500, 'body' => 'No auth data found'];
    }

    try {
        $api = new SimpleBigCommerceAPI($storeHash, $authData['access_token'], getClientId());
        $store = $api->getStore();
        $products = $api->getProducts(5);

        $html = '
        <h1>Welcome to Your BigCommerce App</h1>
        <h2>Store Information</h2>';

        if ($store) {
            $html .= '
            <p><strong>Store Name:</strong> ' . htmlspecialchars($store['name'] ?? 'N/A') . '</p>
            <p><strong>Store URL:</strong> ' . htmlspecialchars($store['domain'] ?? 'N/A') . '</p>';
        }

        $html .= '<p><strong>User:</strong> ' . htmlspecialchars($user['email']) . '</p>

        <h2>Recent Products</h2>
        <ul>';

        foreach ($products as $product) {
            $html .= '<li>' . htmlspecialchars($product['name'] ?? 'Unknown') . ' - $' . number_format($product['price'] ?? 0, 2) . '</li>';
        }

        $html .= '</ul>
        <p><em>This is a basic demonstration of your BigCommerce app integration.</em></p>';

        return [
            'status' => 200,
            'headers' => ['Content-Type' => 'text/html'],
            'body' => $html
        ];

    } catch (Exception $e) {
        error_log('Load callback error: ' . $e->getMessage());
        return ['status' => 500, 'body' => 'Error loading app: ' . $e->getMessage()];
    }
}

function handleUninstall() {
    global $storage;

    $signedPayload = $_GET['signed_payload'] ?? '';

    if (!$signedPayload) {
        return ['status' => 400, 'body' => 'Missing signed_payload'];
    }

    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return ['status' => 401, 'body' => 'Invalid signed_payload'];
    }

    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];

    // Clean up stored data
    $authKey = "stores/{$storeHash}/auth";
    $userKey = getUserKey($storeHash, $userEmail);

    $storage->delete($authKey);
    $storage->delete($userKey);

    return ['status' => 200, 'body' => 'App uninstalled successfully'];
}

function handleRemoveUser() {
    global $storage;

    $signedPayload = $_GET['signed_payload'] ?? '';

    if (!$signedPayload) {
        return ['status' => 400, 'body' => 'Missing signed_payload'];
    }

    $data = verifySignedRequest($signedPayload);
    if (!$data) {
        return ['status' => 401, 'body' => 'Invalid signed_payload'];
    }

    $storeHash = $data['store_hash'];
    $userEmail = $data['user']['email'];

    // Remove user data
    $userKey = getUserKey($storeHash, $userEmail);
    $storage->delete($userKey);

    return ['status' => 200, 'body' => 'User removed successfully'];
}

function handleWebhook() {
    // Get raw POST data
    $payload = file_get_contents('php://input');
    $signature = $_SERVER['HTTP_X_BC_WEBHOOK_SIGNATURE'] ?? '';

    if (!verifyWebhookSignature($payload, $signature)) {
        return ['status' => 401, 'body' => 'Invalid webhook signature'];
    }

    $data = json_decode($payload, true);

    // Log webhook for debugging
    error_log('Webhook received: ' . $payload);

    // Handle different webhook events
    switch ($data['scope'] ?? '') {
        case 'store/order/created':
            // Handle new order
            break;
        case 'store/product/updated':
            // Handle product update
            break;
        default:
            // Handle other events
            break;
    }

    return ['status' => 200, 'body' => 'Webhook processed'];
}

// Helper functions
function getClientId() {
    return $_ENV['BC_CLIENT_ID'] ?? '';
}

function getClientSecret() {
    return $_ENV['BC_CLIENT_SECRET'] ?? '';
}

function getCallbackUrl() {
    return $_ENV['BC_CALLBACK_URL'] ?? '';
}

function getAuthService() {
    return $_ENV['BC_AUTH_SERVICE'] ?? 'https://login.bigcommerce.com';
}

function getUserKey($storeHash, $email) {
    return "app:users:{$storeHash}:{$email}";
}

function verifySignedRequest($signedRequest) {
    if (!$signedRequest) {
        return null;
    }

    $parts = explode('.', $signedRequest, 2);
    if (count($parts) !== 2) {
        return null;
    }

    list($encodedData, $encodedSignature) = $parts;

    $signature = base64_decode($encodedSignature);
    $jsonStr = base64_decode($encodedData);
    $data = json_decode($jsonStr, true);

    $expectedSignature = hash_hmac('sha256', $jsonStr, getClientSecret(), false);

    if (!hash_equals($expectedSignature, $signature)) {
        error_log('Invalid signed request signature');
        return null;
    }

    return $data;
}

function verifyWebhookSignature($payload, $signature) {
    $expectedSignature = hash_hmac('sha256', $payload, getClientSecret(), false);
    return hash_equals($expectedSignature, $signature);
}

function sendResponse($result) {
    http_response_code($result['status']);

    if (isset($result['headers'])) {
        foreach ($result['headers'] as $key => $value) {
            header("$key: $value");
        }
    }

    echo $result['body'];
}

// Main application logic
$result = handleRequest();
sendResponse($result);
