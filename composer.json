{"name": "custom/bigcommerce-app", "description": "Custom BigCommerce App with OAuth, Webhooks, and API Integration", "type": "project", "license": "MIT", "authors": [{"name": "Developer", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "guzzlehttp/guzzle": "^7.8", "vlucas/phpdotenv": "^5.5", "monolog/monolog": "^3.5", "firebase/php-jwt": "^6.8", "ramsey/uuid": "^4.7", "league/oauth2-client": "^2.7", "symfony/http-foundation": "^6.4", "twig/twig": "^3.8", "doctrine/dbal": "^3.7"}, "require-dev": {"phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.8", "phpstan/phpstan": "^1.10"}, "autoload": {"psr-4": {"App\\": "src/", "App\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyze": "phpstan analyse src/ --level=8"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}