<?php

/**
 * Debug script to check routing and environment
 */

echo "<h1>BigCommerce App Debug</h1>";

echo "<h2>Server Information</h2>";
echo "<p><strong>REQUEST_URI:</strong> " . htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>REQUEST_METHOD:</strong> " . htmlspecialchars($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . htmlspecialchars($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . htmlspecialchars($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";

$fullPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
echo "<p><strong>Parsed Path:</strong> " . htmlspecialchars($fullPath) . "</p>";

// Test path extraction
$basePath = '/php/custom_bigcommerce_app';
if (strpos($fullPath, $basePath) === 0) {
    $path = substr($fullPath, strlen($basePath));
} else {
    $path = $fullPath;
}

if (empty($path) || $path === '') {
    $path = '/';
}

echo "<p><strong>Extracted Path:</strong> " . htmlspecialchars($path) . "</p>";

echo "<h2>Environment Variables</h2>";
if (file_exists(__DIR__ . '/.env')) {
    echo "<p>✓ .env file exists</p>";
    
    // Load environment variables
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
    
    echo "<p><strong>BC_CLIENT_ID:</strong> " . (isset($_ENV['BC_CLIENT_ID']) ? 'Set (' . substr($_ENV['BC_CLIENT_ID'], 0, 10) . '...)' : 'Not set') . "</p>";
    echo "<p><strong>BC_CALLBACK_URL:</strong> " . htmlspecialchars($_ENV['BC_CALLBACK_URL'] ?? 'Not set') . "</p>";
} else {
    echo "<p>✗ .env file not found</p>";
}

echo "<h2>Test Routes</h2>";
echo "<ul>";
echo "<li><a href='?test=home'>Test Home Route</a></li>";
echo "<li><a href='?test=load'>Test Load Route</a></li>";
echo "<li><a href='index.php'>Direct index.php access</a></li>";
echo "</ul>";

if (isset($_GET['test'])) {
    echo "<h2>Route Test Result</h2>";
    
    switch ($_GET['test']) {
        case 'home':
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<h3>Home Route Response:</h3>";
            echo "<p>This would be the home page content.</p>";
            echo "</div>";
            break;
            
        case 'load':
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<h3>Load Route Response:</h3>";
            echo "<p>Missing signed_payload (this is expected without BigCommerce authentication)</p>";
            echo "</div>";
            break;
    }
}

echo "<h2>File System Check</h2>";
echo "<p><strong>Current Directory:</strong> " . htmlspecialchars(__DIR__) . "</p>";
echo "<p><strong>index.php exists:</strong> " . (file_exists(__DIR__ . '/index.php') ? '✓ Yes' : '✗ No') . "</p>";
echo "<p><strong>Storage directory:</strong> " . (is_dir(__DIR__ . '/storage') ? '✓ Exists' : '✗ Missing') . "</p>";

echo "<h2>PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>cURL Extension:</strong> " . (extension_loaded('curl') ? '✓ Available' : '✗ Missing') . "</p>";

?>
