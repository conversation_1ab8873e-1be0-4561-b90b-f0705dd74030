<?php

/**
 * Simple installation script for BigCommerce App
 */

echo "BigCommerce App Installation\n";
echo "============================\n\n";

// Step 1: Create .env file if it doesn't exist
if (!file_exists('.env')) {
    echo "Creating .env file...\n";
    copy('.env.example', '.env');
    echo "✓ .env file created from .env.example\n";
} else {
    echo "✓ .env file already exists\n";
}

// Step 2: Create storage directory
$storageDir = __DIR__ . '/storage';
if (!is_dir($storageDir)) {
    echo "Creating storage directory...\n";
    mkdir($storageDir, 0755, true);
    echo "✓ Storage directory created\n";
} else {
    echo "✓ Storage directory already exists\n";
}

// Step 3: Set proper permissions
if (chmod($storageDir, 0755)) {
    echo "✓ Storage directory permissions set\n";
} else {
    echo "⚠ Could not set storage directory permissions\n";
}

// Step 4: Create .htaccess if it doesn't exist
if (!file_exists('.htaccess')) {
    echo "Creating .htaccess file...\n";
    $htaccess = 'RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options SAMEORIGIN
Header always set X-XSS-Protection "1; mode=block"

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>';
    
    file_put_contents('.htaccess', $htaccess);
    echo "✓ .htaccess file created\n";
} else {
    echo "✓ .htaccess file already exists\n";
}

echo "\nInstallation completed!\n";
echo "\nNext steps:\n";
echo "1. Edit .env file and add your BigCommerce app credentials:\n";
echo "   - BC_CLIENT_ID=your_client_id\n";
echo "   - BC_CLIENT_SECRET=your_client_secret\n";
echo "   - BC_CALLBACK_URL=https://yourdomain.com/auth/callback\n";
echo "\n2. Start the development server:\n";
echo "   php -S localhost:8000\n";
echo "\n3. For public access, use ngrok:\n";
echo "   ngrok http 8000\n";
echo "\n4. Register your app in BigCommerce Developer Portal:\n";
echo "   https://devtools.bigcommerce.com/\n";
echo "\n5. Test your installation:\n";
echo "   php test.php\n";

echo "\nYour BigCommerce app is ready to use!\n";
